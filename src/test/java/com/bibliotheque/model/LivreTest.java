package com.bibliotheque.model;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests unitaires pour la classe Livre
 */
class LivreTest {
    
    @Test
    @DisplayName("Test du constructeur complet")
    void testConstructeurComplet() {
        Livre livre = new Livre("1984", "<PERSON>", "978-0451524935", 1949);
        
        assertEquals("1984", livre.getTitre());
        assertEquals("<PERSON>", livre.getAuteur());
        assertEquals("978-0451524935", livre.getIsbn());
        assertEquals(1949, livre.getAnneePublication());
    }
    
    @Test
    @DisplayName("Test du constructeur simplifié")
    void testConstructeurSimplifie() {
        Livre livre = new Livre("Le Petit Prince", "<PERSON>-Exupé<PERSON>");
        
        assertEquals("Le Petit Prince", livre.getTitre());
        assertEquals("<PERSON>", livre.getAuteur());
        assertEquals("", livre.getIsbn());
        assertEquals(0, livre.getAnneePublication());
    }
    
    @Test
    @DisplayName("Test des setters")
    void testSetters() {
        Livre livre = new Livre("Titre Initial", "Auteur Initial");
        
        livre.setTitre("Nouveau Titre");
        livre.setAuteur("Nouvel Auteur");
        livre.setIsbn("978-1234567890");
        livre.setAnneePublication(2023);
        
        assertEquals("Nouveau Titre", livre.getTitre());
        assertEquals("Nouvel Auteur", livre.getAuteur());
        assertEquals("978-1234567890", livre.getIsbn());
        assertEquals(2023, livre.getAnneePublication());
    }
    
    @Test
    @DisplayName("Test de l'égalité entre livres")
    void testEgalite() {
        Livre livre1 = new Livre("1984", "George Orwell");
        Livre livre2 = new Livre("1984", "Autre Auteur");
        Livre livre3 = new Livre("Animal Farm", "George Orwell");
        
        // Deux livres avec le même titre sont égaux (insensible à la casse)
        assertEquals(livre1, livre2);
        
        // Deux livres avec des titres différents ne sont pas égaux
        assertNotEquals(livre1, livre3);
        
        // Test avec différentes casses
        Livre livre4 = new Livre("1984", "George Orwell");
        Livre livre5 = new Livre("1984", "George Orwell");
        assertEquals(livre4, livre5);
    }
    
    @Test
    @DisplayName("Test de l'égalité insensible à la casse")
    void testEgaliteInsensibleCasse() {
        Livre livre1 = new Livre("Le Petit Prince", "Antoine de Saint-Exupéry");
        Livre livre2 = new Livre("le petit prince", "Autre Auteur");
        Livre livre3 = new Livre("LE PETIT PRINCE", "Encore un autre");
        
        assertEquals(livre1, livre2);
        assertEquals(livre1, livre3);
        assertEquals(livre2, livre3);
    }
    
    @Test
    @DisplayName("Test du hashCode")
    void testHashCode() {
        Livre livre1 = new Livre("1984", "George Orwell");
        Livre livre2 = new Livre("1984", "Autre Auteur");
        
        // Deux livres égaux doivent avoir le même hashCode
        assertEquals(livre1.hashCode(), livre2.hashCode());
    }
    
    @Test
    @DisplayName("Test de toString avec tous les champs")
    void testToStringComplet() {
        Livre livre = new Livre("1984", "George Orwell", "978-0451524935", 1949);
        String toString = livre.toString();
        
        assertTrue(toString.contains("1984"));
        assertTrue(toString.contains("George Orwell"));
        assertTrue(toString.contains("978-0451524935"));
        assertTrue(toString.contains("1949"));
    }
    
    @Test
    @DisplayName("Test de toString avec champs partiels")
    void testToStringPartiel() {
        Livre livre = new Livre("Le Petit Prince", "Antoine de Saint-Exupéry");
        String toString = livre.toString();
        
        assertTrue(toString.contains("Le Petit Prince"));
        assertTrue(toString.contains("Antoine de Saint-Exupéry"));
        // ISBN et année ne doivent pas apparaître car ils sont vides/zéro
        assertFalse(toString.contains("isbn=''"));
        assertFalse(toString.contains("anneePublication=0"));
    }
    
    @Test
    @DisplayName("Test d'égalité avec null et autres objets")
    void testEgaliteAvecNull() {
        Livre livre = new Livre("1984", "George Orwell");
        
        assertNotEquals(livre, null);
        assertNotEquals(livre, "Une chaîne");
        assertNotEquals(livre, 42);
        assertEquals(livre, livre); // Réflexivité
    }
}
