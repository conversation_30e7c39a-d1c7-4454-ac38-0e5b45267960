package com.bibliotheque.service;

import com.bibliotheque.model.Livre;
import com.bibliotheque.exception.LivreDejaExistantException;
import com.bibliotheque.exception.LivreIntrouvableException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests unitaires pour la classe Bibliotheque
 */
class BibliothequeTest {
    
    private Bibliotheque bibliotheque;
    private Livre livre1;
    private Livre livre2;
    
    @BeforeEach
    void setUp() {
        bibliotheque = new Bibliotheque();
        livre1 = new Livre("Le Petit Prince", "Antoine de Saint-Exupéry", "978-2070408504", 1943);
        livre2 = new Livre("1984", "George Orwell", "978-0451524935", 1949);
    }
    
    @Test
    @DisplayName("Test d'ajout d'un livre valide")
    void testAjouterLivreValide() {
        assertDoesNotThrow(() -> bibliotheque.ajouterLivre(livre1));
        assertEquals(1, bibliotheque.getNombreLivres());
        assertFalse(bibliotheque.estVide());
    }
    
    @Test
    @DisplayName("Test d'ajout d'un livre déjà existant")
    void testAjouterLivreDejaExistant() throws LivreDejaExistantException {
        bibliotheque.ajouterLivre(livre1);
        
        Livre livreDouble = new Livre("Le Petit Prince", "Autre Auteur");
        
        LivreDejaExistantException exception = assertThrows(
            LivreDejaExistantException.class,
            () -> bibliotheque.ajouterLivre(livreDouble)
        );
        
        assertTrue(exception.getMessage().contains("Le Petit Prince"));
        assertEquals(1, bibliotheque.getNombreLivres());
    }
    
    @Test
    @DisplayName("Test d'ajout d'un livre null")
    void testAjouterLivreNull() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> bibliotheque.ajouterLivre(null)
        );
        
        assertEquals("Le livre ne peut pas être null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Test d'ajout d'un livre avec titre vide")
    void testAjouterLivreTitreVide() {
        Livre livreVide = new Livre("", "Auteur Test");
        
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> bibliotheque.ajouterLivre(livreVide)
        );
        
        assertTrue(exception.getMessage().contains("titre"));
    }
    
    @Test
    @DisplayName("Test de recherche d'un livre existant")
    void testRechercherLivreExistant() throws LivreDejaExistantException, LivreIntrouvableException {
        bibliotheque.ajouterLivre(livre1);
        
        Livre livreRecherche = bibliotheque.rechercherLivre("Le Petit Prince");
        
        assertNotNull(livreRecherche);
        assertEquals("Le Petit Prince", livreRecherche.getTitre());
        assertEquals("Antoine de Saint-Exupéry", livreRecherche.getAuteur());
    }
    
    @Test
    @DisplayName("Test de recherche d'un livre inexistant")
    void testRechercherLivreInexistant() {
        LivreIntrouvableException exception = assertThrows(
            LivreIntrouvableException.class,
            () -> bibliotheque.rechercherLivre("Livre Inexistant")
        );
        
        assertTrue(exception.getMessage().contains("Livre Inexistant"));
    }
    
    @Test
    @DisplayName("Test de recherche avec titre null")
    void testRechercherLivreTitreNull() {
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> bibliotheque.rechercherLivre(null)
        );
        
        assertTrue(exception.getMessage().contains("titre"));
    }
    
    @Test
    @DisplayName("Test de suppression d'un livre existant")
    void testSupprimerLivreExistant() throws LivreDejaExistantException, LivreIntrouvableException {
        bibliotheque.ajouterLivre(livre1);
        bibliotheque.ajouterLivre(livre2);
        assertEquals(2, bibliotheque.getNombreLivres());
        
        bibliotheque.supprimerLivre("Le Petit Prince");
        
        assertEquals(1, bibliotheque.getNombreLivres());
        assertThrows(LivreIntrouvableException.class, 
                    () -> bibliotheque.rechercherLivre("Le Petit Prince"));
    }
    
    @Test
    @DisplayName("Test de suppression d'un livre inexistant")
    void testSupprimerLivreInexistant() {
        LivreIntrouvableException exception = assertThrows(
            LivreIntrouvableException.class,
            () -> bibliotheque.supprimerLivre("Livre Inexistant")
        );
        
        assertTrue(exception.getMessage().contains("Livre Inexistant"));
    }
    
    @Test
    @DisplayName("Test de recherche par auteur")
    void testRechercherParAuteur() throws LivreDejaExistantException {
        bibliotheque.ajouterLivre(livre1);
        bibliotheque.ajouterLivre(livre2);
        bibliotheque.ajouterLivre(new Livre("Animal Farm", "George Orwell"));
        
        List<Livre> livresOrwell = bibliotheque.rechercherParAuteur("George Orwell");
        
        assertEquals(2, livresOrwell.size());
        assertTrue(livresOrwell.stream().allMatch(livre -> 
                  livre.getAuteur().contains("George Orwell")));
    }
    
    @Test
    @DisplayName("Test de recherche par auteur inexistant")
    void testRechercherParAuteurInexistant() throws LivreDejaExistantException {
        bibliotheque.ajouterLivre(livre1);
        
        List<Livre> livres = bibliotheque.rechercherParAuteur("Auteur Inexistant");
        
        assertTrue(livres.isEmpty());
    }
    
    @Test
    @DisplayName("Test de bibliothèque vide")
    void testBibliothequeVide() {
        assertTrue(bibliotheque.estVide());
        assertEquals(0, bibliotheque.getNombreLivres());
    }
    
    @Test
    @DisplayName("Test de recherche insensible à la casse")
    void testRechercheInsensibleCasse() throws LivreDejaExistantException, LivreIntrouvableException {
        bibliotheque.ajouterLivre(livre1);
        
        // Test avec différentes casses
        assertDoesNotThrow(() -> bibliotheque.rechercherLivre("le petit prince"));
        assertDoesNotThrow(() -> bibliotheque.rechercherLivre("LE PETIT PRINCE"));
        assertDoesNotThrow(() -> bibliotheque.rechercherLivre("Le PETIT prince"));
    }
}
