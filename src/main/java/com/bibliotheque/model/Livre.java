package com.bibliotheque.model;

import java.util.Objects;

/**
 * Classe représentant un livre dans la bibliothèque
 */
public class Livre {
    private String titre;
    private String auteur;
    private String isbn;
    private int anneePublication;
    
    /**
     * Constructeur avec tous les paramètres
     */
    public Livre(String titre, String auteur, String isbn, int anneePublication) {
        this.titre = titre;
        this.auteur = auteur;
        this.isbn = isbn;
        this.anneePublication = anneePublication;
    }
    
    /**
     * Constructeur simplifié avec titre et auteur
     */
    public Livre(String titre, String auteur) {
        this.titre = titre;
        this.auteur = auteur;
        this.isbn = "";
        this.anneePublication = 0;
    }
    
    // Getters
    public String getTitre() {
        return titre;
    }
    
    public String getAuteur() {
        return auteur;
    }
    
    public String getIsbn() {
        return isbn;
    }
    
    public int getAnneePublication() {
        return anneePublication;
    }
    
    // Setters
    public void setTitre(String titre) {
        this.titre = titre;
    }
    
    public void setAuteur(String auteur) {
        this.auteur = auteur;
    }
    
    public void setIsbn(String isbn) {
        this.isbn = isbn;
    }
    
    public void setAnneePublication(int anneePublication) {
        this.anneePublication = anneePublication;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Livre livre = (Livre) obj;
        return Objects.equals(titre.toLowerCase(), livre.titre.toLowerCase());
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(titre.toLowerCase());
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Livre{");
        sb.append("titre='").append(titre).append('\'');
        sb.append(", auteur='").append(auteur).append('\'');
        if (!isbn.isEmpty()) {
            sb.append(", isbn='").append(isbn).append('\'');
        }
        if (anneePublication > 0) {
            sb.append(", anneePublication=").append(anneePublication);
        }
        sb.append('}');
        return sb.toString();
    }
}
