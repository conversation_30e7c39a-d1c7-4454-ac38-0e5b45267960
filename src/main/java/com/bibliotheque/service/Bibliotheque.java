package com.bibliotheque.service;

import com.bibliotheque.model.Livre;
import com.bibliotheque.exception.LivreDejaExistantException;
import com.bibliotheque.exception.LivreIntrouvableException;

import java.util.ArrayList;
import java.util.List;

/**
 * Classe pour gérer une bibliothèque de livres
 */
public class Bibliotheque {
    private List<Livre> livres;
    
    /**
     * Constructeur
     */
    public Bibliotheque() {
        this.livres = new ArrayList<>();
    }
    
    /**
     * Ajouter un livre à la bibliothèque
     * @param livre Le livre à ajouter
     * @throws LivreDejaExistantException Si un livre avec le même titre existe déjà
     */
    public void ajouterLivre(Livre livre) throws LivreDejaExistantException {
        if (livre == null) {
            throw new IllegalArgumentException("Le livre ne peut pas être null");
        }
        
        if (livre.getTitre() == null || livre.getTitre().trim().isEmpty()) {
            throw new IllegalArgumentException("Le titre du livre ne peut pas être vide");
        }
        
        // Vérifier si le livre existe déjà
        if (livreExiste(livre.getTitre())) {
            throw new LivreDejaExistantException("Un livre avec le titre '" + livre.getTitre() + "' existe déjà dans la bibliothèque");
        }
        
        livres.add(livre);
        System.out.println("Livre ajouté avec succès : " + livre.getTitre());
    }
    
    /**
     * Supprimer un livre par son titre
     * @param titre Le titre du livre à supprimer
     * @throws LivreIntrouvableException Si le livre n'existe pas
     */
    public void supprimerLivre(String titre) throws LivreIntrouvableException {
        if (titre == null || titre.trim().isEmpty()) {
            throw new IllegalArgumentException("Le titre ne peut pas être vide");
        }
        
        Livre livreASupprimer = null;
        for (Livre livre : livres) {
            if (livre.getTitre().equalsIgnoreCase(titre.trim())) {
                livreASupprimer = livre;
                break;
            }
        }
        
        if (livreASupprimer == null) {
            throw new LivreIntrouvableException("Aucun livre trouvé avec le titre '" + titre + "'");
        }
        
        livres.remove(livreASupprimer);
        System.out.println("Livre supprimé avec succès : " + titre);
    }
    
    /**
     * Rechercher un livre par titre
     * @param titre Le titre du livre à rechercher
     * @return Le livre trouvé
     * @throws LivreIntrouvableException Si le livre n'existe pas
     */
    public Livre rechercherLivre(String titre) throws LivreIntrouvableException {
        if (titre == null || titre.trim().isEmpty()) {
            throw new IllegalArgumentException("Le titre ne peut pas être vide");
        }
        
        for (Livre livre : livres) {
            if (livre.getTitre().equalsIgnoreCase(titre.trim())) {
                return livre;
            }
        }
        
        throw new LivreIntrouvableException("Aucun livre trouvé avec le titre '" + titre + "'");
    }
    
    /**
     * Afficher la liste de tous les livres disponibles
     */
    public void afficherTousLesLivres() {
        if (livres.isEmpty()) {
            System.out.println("La bibliothèque est vide.");
            return;
        }
        
        System.out.println("\n=== Liste des livres dans la bibliothèque ===");
        System.out.println("Nombre total de livres : " + livres.size());
        System.out.println("--------------------------------------------");
        
        for (int i = 0; i < livres.size(); i++) {
            System.out.println((i + 1) + ". " + livres.get(i));
        }
        System.out.println("============================================\n");
    }
    
    /**
     * Rechercher des livres par auteur
     * @param auteur L'auteur à rechercher
     * @return Liste des livres de cet auteur
     */
    public List<Livre> rechercherParAuteur(String auteur) {
        List<Livre> livresAuteur = new ArrayList<>();
        
        if (auteur == null || auteur.trim().isEmpty()) {
            return livresAuteur;
        }
        
        for (Livre livre : livres) {
            if (livre.getAuteur() != null && 
                livre.getAuteur().toLowerCase().contains(auteur.toLowerCase().trim())) {
                livresAuteur.add(livre);
            }
        }
        
        return livresAuteur;
    }
    
    /**
     * Vérifier si un livre existe par titre
     * @param titre Le titre à vérifier
     * @return true si le livre existe, false sinon
     */
    private boolean livreExiste(String titre) {
        if (titre == null) return false;
        
        for (Livre livre : livres) {
            if (livre.getTitre().equalsIgnoreCase(titre.trim())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Obtenir le nombre de livres dans la bibliothèque
     * @return Le nombre de livres
     */
    public int getNombreLivres() {
        return livres.size();
    }
    
    /**
     * Vérifier si la bibliothèque est vide
     * @return true si vide, false sinon
     */
    public boolean estVide() {
        return livres.isEmpty();
    }
}
