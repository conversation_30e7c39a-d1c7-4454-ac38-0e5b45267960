package com.bibliotheque;

import com.bibliotheque.model.Livre;
import com.bibliotheque.service.Bibliotheque;
import com.bibliotheque.exception.LivreDejaExistantException;
import com.bibliotheque.exception.LivreIntrouvableException;

import java.util.List;

/**
 * Classe principale pour tester l'application de gestion de bibliothèque
 */
public class Main {
    
    public static void main(String[] args) {
        System.out.println("=== Application de Gestion de Bibliothèque ===\n");
        
        // Créer une instance de bibliothèque
        Bibliotheque bibliotheque = new Bibliotheque();
        
        // Test 1: Affichage d'une bibliothèque vide
        System.out.println("1. Test d'affichage d'une bibliothèque vide:");
        bibliotheque.afficherTousLesLivres();
        
        // Test 2: Ajout de livres
        System.out.println("2. Test d'ajout de livres:");
        try {
            bibliotheque.ajouterLivre(new Livre("Le Petit Prince", "<PERSON>", "978-2070408504", 1943));
            bibliotheque.ajouterLivre(new Livre("1984", "<PERSON>well", "978-0451524935", 1949));
            bibliotheque.ajouterLivre(new Livre("L'Étranger", "Albert Camus", "978-2070360024", 1942));
            bibliotheque.ajouterLivre(new Livre("Les Misérables", "Victor Hugo"));
            
        } catch (LivreDejaExistantException e) {
            System.err.println("Erreur lors de l'ajout : " + e.getMessage());
        }
        
        // Test 3: Affichage de tous les livres
        System.out.println("\n3. Test d'affichage de tous les livres:");
        bibliotheque.afficherTousLesLivres();
        
        // Test 4: Recherche d'un livre existant
        System.out.println("4. Test de recherche d'un livre existant:");
        try {
            Livre livre = bibliotheque.rechercherLivre("1984");
            System.out.println("Livre trouvé : " + livre);
        } catch (LivreIntrouvableException e) {
            System.err.println("Erreur de recherche : " + e.getMessage());
        }
        
        // Test 5: Tentative d'ajout d'un livre déjà existant
        System.out.println("\n5. Test d'ajout d'un livre déjà existant:");
        try {
            bibliotheque.ajouterLivre(new Livre("1984", "George Orwell"));
        } catch (LivreDejaExistantException e) {
            System.err.println("Exception attendue : " + e.getMessage());
        }
        
        // Test 6: Recherche d'un livre inexistant
        System.out.println("\n6. Test de recherche d'un livre inexistant:");
        try {
            bibliotheque.rechercherLivre("Livre Inexistant");
        } catch (LivreIntrouvableException e) {
            System.err.println("Exception attendue : " + e.getMessage());
        }
        
        // Test 7: Suppression d'un livre existant
        System.out.println("\n7. Test de suppression d'un livre existant:");
        try {
            bibliotheque.supprimerLivre("L'Étranger");
        } catch (LivreIntrouvableException e) {
            System.err.println("Erreur de suppression : " + e.getMessage());
        }
        
        // Test 8: Affichage après suppression
        System.out.println("\n8. Affichage après suppression:");
        bibliotheque.afficherTousLesLivres();
        
        // Test 9: Tentative de suppression d'un livre inexistant
        System.out.println("9. Test de suppression d'un livre inexistant:");
        try {
            bibliotheque.supprimerLivre("Livre Inexistant");
        } catch (LivreIntrouvableException e) {
            System.err.println("Exception attendue : " + e.getMessage());
        }
        
        // Test 10: Recherche par auteur
        System.out.println("\n10. Test de recherche par auteur:");
        List<Livre> livresVictor = bibliotheque.rechercherParAuteur("Victor Hugo");
        if (!livresVictor.isEmpty()) {
            System.out.println("Livres de Victor Hugo trouvés :");
            for (Livre livre : livresVictor) {
                System.out.println("  - " + livre);
            }
        } else {
            System.out.println("Aucun livre de Victor Hugo trouvé.");
        }
        
        // Test 11: Gestion des cas limites
        System.out.println("\n11. Test des cas limites:");
        try {
            // Tentative d'ajout d'un livre null
            bibliotheque.ajouterLivre(null);
        } catch (IllegalArgumentException e) {
            System.err.println("Exception attendue (livre null) : " + e.getMessage());
        } catch (LivreDejaExistantException e) {
            System.err.println("Erreur inattendue : " + e.getMessage());
        }
        
        try {
            // Tentative d'ajout d'un livre avec titre vide
            bibliotheque.ajouterLivre(new Livre("", "Auteur Test"));
        } catch (IllegalArgumentException e) {
            System.err.println("Exception attendue (titre vide) : " + e.getMessage());
        } catch (LivreDejaExistantException e) {
            System.err.println("Erreur inattendue : " + e.getMessage());
        }
        
        try {
            // Tentative de recherche avec titre null
            bibliotheque.rechercherLivre(null);
        } catch (IllegalArgumentException e) {
            System.err.println("Exception attendue (recherche null) : " + e.getMessage());
        } catch (LivreIntrouvableException e) {
            System.err.println("Erreur inattendue : " + e.getMessage());
        }
        
        // Test 12: Statistiques finales
        System.out.println("\n12. Statistiques finales:");
        System.out.println("Nombre total de livres dans la bibliothèque : " + bibliotheque.getNombreLivres());
        System.out.println("La bibliothèque est-elle vide ? " + bibliotheque.estVide());
        
        System.out.println("\n=== Fin des tests ===");
    }
}
